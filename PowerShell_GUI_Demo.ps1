# PowerShell GUI 演示脚本
# 包含输入框、按钮、编辑框和消息框功能

Add-Type -AssemblyName System.Windows.Forms
Add-Type -AssemblyName System.Drawing

# 创建主窗体
$form = New-Object System.Windows.Forms.Form
$form.Text = "PowerShell GUI 演示"
$form.Size = New-Object System.Drawing.Size(500, 400)
$form.StartPosition = "CenterScreen"
$form.FormBorderStyle = "FixedDialog"
$form.MaximizeBox = $false

# 创建标签 - 输入框说明
$labelInput = New-Object System.Windows.Forms.Label
$labelInput.Location = New-Object System.Drawing.Point(20, 20)
$labelInput.Size = New-Object System.Drawing.Size(200, 20)
$labelInput.Text = "请输入内容："
$form.Controls.Add($labelInput)

# 创建输入框
$textBoxInput = New-Object System.Windows.Forms.TextBox
$textBoxInput.Location = New-Object System.Drawing.Point(20, 45)
$textBoxInput.Size = New-Object System.Drawing.Size(300, 25)
$textBoxInput.Font = New-Object System.Drawing.Font("Microsoft YaHei", 10)
$form.Controls.Add($textBoxInput)

# 创建按钮
$buttonProcess = New-Object System.Windows.Forms.Button
$buttonProcess.Location = New-Object System.Drawing.Point(340, 45)
$buttonProcess.Size = New-Object System.Drawing.Size(100, 25)
$buttonProcess.Text = "处理内容"
$buttonProcess.Font = New-Object System.Drawing.Font("Microsoft YaHei", 9)
$buttonProcess.BackColor = [System.Drawing.Color]::LightBlue
$form.Controls.Add($buttonProcess)

# 创建标签 - 输出框说明
$labelOutput = New-Object System.Windows.Forms.Label
$labelOutput.Location = New-Object System.Drawing.Point(20, 90)
$labelOutput.Size = New-Object System.Drawing.Size(200, 20)
$labelOutput.Text = "输出结果："
$form.Controls.Add($labelOutput)

# 创建多行编辑框（输出区域）
$textBoxOutput = New-Object System.Windows.Forms.TextBox
$textBoxOutput.Location = New-Object System.Drawing.Point(20, 115)
$textBoxOutput.Size = New-Object System.Drawing.Size(420, 150)
$textBoxOutput.Multiline = $true
$textBoxOutput.ScrollBars = "Vertical"
$textBoxOutput.ReadOnly = $true
$textBoxOutput.Font = New-Object System.Drawing.Font("Consolas", 10)
$textBoxOutput.BackColor = [System.Drawing.Color]::LightGray
$form.Controls.Add($textBoxOutput)

# 创建清空按钮
$buttonClear = New-Object System.Windows.Forms.Button
$buttonClear.Location = New-Object System.Drawing.Point(20, 280)
$buttonClear.Size = New-Object System.Drawing.Size(80, 30)
$buttonClear.Text = "清空"
$buttonClear.Font = New-Object System.Drawing.Font("Microsoft YaHei", 9)
$buttonClear.BackColor = [System.Drawing.Color]::LightCoral
$form.Controls.Add($buttonClear)

# 创建退出按钮
$buttonExit = New-Object System.Windows.Forms.Button
$buttonExit.Location = New-Object System.Drawing.Point(360, 280)
$buttonExit.Size = New-Object System.Drawing.Size(80, 30)
$buttonExit.Text = "退出"
$buttonExit.Font = New-Object System.Drawing.Font("Microsoft YaHei", 9)
$buttonExit.BackColor = [System.Drawing.Color]::LightSalmon
$form.Controls.Add($buttonExit)

# 创建状态标签
$statusLabel = New-Object System.Windows.Forms.Label
$statusLabel.Location = New-Object System.Drawing.Point(20, 320)
$statusLabel.Size = New-Object System.Drawing.Size(420, 20)
$statusLabel.Text = "就绪 - 请输入内容并点击处理按钮"
$statusLabel.ForeColor = [System.Drawing.Color]::Blue
$form.Controls.Add($statusLabel)

# 按钮点击事件处理
$buttonProcess.Add_Click({
    $inputText = $textBoxInput.Text.Trim()
    
    if ([string]::IsNullOrEmpty($inputText)) {
        # 显示警告消息框
        [System.Windows.Forms.MessageBox]::Show(
            "请先输入一些内容！", 
            "输入为空", 
            [System.Windows.Forms.MessageBoxButtons]::OK, 
            [System.Windows.Forms.MessageBoxIcon]::Warning
        )
        $textBoxInput.Focus()
        return
    }
    
    # 获取当前时间
    $currentTime = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    
    # 在输出框中显示内容
    $outputText = "[$currentTime] 您输入的内容是：`r`n$inputText`r`n" + "-" * 50 + "`r`n"
    
    if ($textBoxOutput.Text -eq "") {
        $textBoxOutput.Text = $outputText
    } else {
        $textBoxOutput.Text = $textBoxOutput.Text + "`r`n" + $outputText
    }
    
    # 滚动到底部
    $textBoxOutput.SelectionStart = $textBoxOutput.Text.Length
    $textBoxOutput.ScrollToCaret()
    
    # 显示成功消息框
    $result = [System.Windows.Forms.MessageBox]::Show(
        "内容已成功处理并显示在输出区域！`r`n`r`n输入内容：$inputText", 
        "处理完成", 
        [System.Windows.Forms.MessageBoxButtons]::OK, 
        [System.Windows.Forms.MessageBoxIcon]::Information
    )
    
    # 更新状态
    $statusLabel.Text = "已处理内容：$inputText"
    $statusLabel.ForeColor = [System.Drawing.Color]::Green
    
    # 清空输入框并聚焦
    $textBoxInput.Clear()
    $textBoxInput.Focus()
})

# 清空按钮事件
$buttonClear.Add_Click({
    $result = [System.Windows.Forms.MessageBox]::Show(
        "确定要清空所有内容吗？", 
        "确认清空", 
        [System.Windows.Forms.MessageBoxButtons]::YesNo, 
        [System.Windows.Forms.MessageBoxIcon]::Question
    )
    
    if ($result -eq [System.Windows.Forms.DialogResult]::Yes) {
        $textBoxInput.Clear()
        $textBoxOutput.Clear()
        $statusLabel.Text = "内容已清空 - 请输入新内容"
        $statusLabel.ForeColor = [System.Drawing.Color]::Orange
        $textBoxInput.Focus()
        
        [System.Windows.Forms.MessageBox]::Show(
            "所有内容已清空！", 
            "清空完成", 
            [System.Windows.Forms.MessageBoxButtons]::OK, 
            [System.Windows.Forms.MessageBoxIcon]::Information
        )
    }
})

# 退出按钮事件
$buttonExit.Add_Click({
    $result = [System.Windows.Forms.MessageBox]::Show(
        "确定要退出程序吗？", 
        "确认退出", 
        [System.Windows.Forms.MessageBoxButtons]::YesNo, 
        [System.Windows.Forms.MessageBoxIcon]::Question
    )
    
    if ($result -eq [System.Windows.Forms.DialogResult]::Yes) {
        $form.Close()
    }
})

# 回车键快捷处理
$textBoxInput.Add_KeyDown({
    if ($_.KeyCode -eq [System.Windows.Forms.Keys]::Enter) {
        $buttonProcess.PerformClick()
    }
})

# 窗体关闭事件
$form.Add_FormClosing({
    $result = [System.Windows.Forms.MessageBox]::Show(
        "感谢使用 PowerShell GUI 演示程序！`r`n再见！", 
        "程序退出", 
        [System.Windows.Forms.MessageBoxButtons]::OK, 
        [System.Windows.Forms.MessageBoxIcon]::Information
    )
})

# 设置初始焦点
$textBoxInput.Focus()

# 显示窗体
Write-Host "正在启动 PowerShell GUI 演示程序..." -ForegroundColor Green
$form.ShowDialog() | Out-Null

Write-Host "程序已退出。" -ForegroundColor Yellow
